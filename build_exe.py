#!/usr/bin/env python3
"""
Build script for KlingCreator unified_multi_session.py
Creates standalone .exe file using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        print(f"Error output: {e.stderr}")
        return False
    return True

def build_exe():
    """Build .exe using PyInstaller with comprehensive imports"""
    print("🔨 Building .exe file...")

    # PyInstaller command - windowed version (no console)
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single .exe file
        "--windowed",                   # NO console window
        "--name=KlingCreator_MultiThread_v2",          # Output name
        "--add-data=kling_wrapper.py;.", # Include wrapper
        "--add-data=kling;kling",       # Include kling folder
        # PyQt6 imports
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--collect-all=PyQt6",
        # Standard library imports
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=json",
        "--hidden-import=threading",
        "--hidden-import=uuid",
        "--hidden-import=datetime",
        # Fix fake_useragent with fallback (already fixed in kling.py)
        "--hidden-import=fake_useragent",
        "--hidden-import=fake_useragent.fake",
        "--hidden-import=fake_useragent.utils",
        "--collect-all=fake_useragent",
        # Rich library
        "--hidden-import=rich",
        "--collect-all=rich",
        # Exclude unnecessary modules to reduce size
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "--exclude-module=scipy",
        "--exclude-module=tensorflow",
        "--exclude-module=torch",
        "unified_multi_session.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def cleanup():
    """Clean up build files"""
    print("🧹 Cleaning up...")
    
    # Remove build directories
    dirs_to_remove = ["build", "__pycache__"]
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   Removed {dir_name}/")
    
    # Remove .spec file
    spec_file = "KlingCreator_MultiThread_v2.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"   Removed {spec_file}")

def main():
    """Main build process"""
    print("🚀 KlingCreator .exe Builder")
    print("=" * 40)
    
    # Check if main file exists
    if not os.path.exists("unified_multi_session.py"):
        print("❌ unified_multi_session.py not found!")
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Build .exe
    if not build_exe():
        return False
    
    # Check output
    exe_path = os.path.join("dist", "KlingCreator_MultiThread_v2.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"🎉 Success! Created: {exe_path}")
        print(f"📊 File size: {file_size:.1f} MB")
        
        # Optional cleanup
        response = input("\n🧹 Clean up build files? (y/n): ").lower()
        if response == 'y':
            cleanup()
        
        print("\n✨ Build complete! Your .exe is in the 'dist' folder.")
        return True
    else:
        print("❌ .exe file not found in dist folder!")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
