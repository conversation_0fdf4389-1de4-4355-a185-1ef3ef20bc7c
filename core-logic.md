# KlingCreator Core Logic Documentation

## Generation Workflow

### Complete Generation Process
```mermaid
sequenceDiagram
    participant User
    participant GUI
    participant SessionData
    participant UnifiedWorkerThread
    participant SafeWrapper
    participant CoreAPI
    participant KlingAI

    User->>GUI: Start Generation
    GUI->>SessionData: Validate Connection
    GUI->>UnifiedWorkerThread: Create Worker
    UnifiedWorkerThread->>SafeWrapper: Call get_video/get_images
    SafeWrapper->>CoreAPI: Delegate with retry logic
    CoreAPI->>KlingAI: Submit generation request
    KlingAI-->>CoreAPI: Return task_id
    CoreAPI->>CoreAPI: Poll for completion
    KlingAI-->>CoreAPI: Return results + work_ids
    CoreAPI-->>SafeWrapper: Return URLs + store work_ids
    SafeWrapper-->>UnifiedWorkerThread: Return validated results
    UnifiedWorkerThread-->>GUI: Emit result_ready signal
    GUI->>SessionData: Store results + work_ids
    GUI->>GUI: Update UI + trigger auto-download
```

### Step-by-Step Generation Logic

#### 1. Pre-Generation Validation
```python
# In UnifiedWorkerThread.run()
1. Validate session connection
2. Check generator availability (image_gen/video_gen)
3. Validate input parameters (prompt, image_path)
4. Test connection with get_account_point()
5. Verify sufficient points/credits
```

#### 2. Generation Execution
```python
# SafeWrapper handles retry logic
for attempt in range(max_retries):
    try:
        result = self.original.get_video(prompt, **kwargs)
        # Validate result is not None, is list, has content
        return result
    except NoneType/API errors:
        if retryable: continue
        else: raise
```

#### 3. Work ID Extraction (Core API)
```python
# In kling.py _get_video_with_payload()
works = image_data.get("works", [])
work_ids = []
for work in works:
    work_id = work.get("workId") or work.get("id") or work.get("work_id")
    if work_id:
        work_ids.append(str(work_id))

# Store globally and as instance attribute
self.last_work_ids = work_ids
global LAST_WORK_IDS
LAST_WORK_IDS = work_ids
```

#### 4. Result Processing
```python
# In GUI on_result_ready()
session_data.last_result = results
session_data.last_content_type = content_type
session_data.last_model = model_version

# Extract work IDs from generator
if content_type == "video" and hasattr(session_data.video_gen, 'last_work_ids'):
    session_data.last_work_ids = session_data.video_gen.last_work_ids
```

## Work ID Extraction and Download Logic

### Current Work ID Flow (WITH BUG)

#### 1. Work ID Storage During Generation
```python
# kling.py stores work IDs in two places:
self.last_work_ids = work_ids           # Instance attribute
LAST_WORK_IDS = work_ids               # Global variable
```

#### 2. Work ID Access in GUI
```python
# unified_multi_session.py on_result_ready()
if content_type == "video" and hasattr(session_data.video_gen, 'last_work_ids'):
    session_data.last_work_ids = session_data.video_gen.last_work_ids
    # ✅ This works correctly
```

#### 3. **BUG**: Download Work ID Retrieval
```python
# download_without_watermark() - PROBLEMATIC CODE
work_id = None
try:
    from kling.kling import LAST_WORK_IDS
    if LAST_WORK_IDS and len(LAST_WORK_IDS) > index:
        work_id = LAST_WORK_IDS[index]  # ❌ Uses global, not session-specific
    else:
        work_id = "283023275526088"     # ❌ Hardcoded fallback
except Exception:
    work_id = "283023275526088"         # ❌ Hardcoded fallback
```

### **Root Cause of Work ID Bug**
1. **Global Variable Pollution**: `LAST_WORK_IDS` is overwritten by each generation
2. **Multi-session Conflict**: Multiple sessions overwrite the same global variable
3. **Ignored Session Data**: `session_data.last_work_ids` is correctly stored but not used
4. **Hardcoded Fallback**: Falls back to test work ID instead of extracting from URL

### **Correct Work ID Logic (Should Be)**
```python
# download_without_watermark() - FIXED VERSION
session_data = self.sessions[session_id]

# Priority 1: Use session-stored work IDs
if session_data.last_work_ids and len(session_data.last_work_ids) > index:
    work_id = session_data.last_work_ids[index]
# Priority 2: Extract from result URL
elif session_data.last_result and len(session_data.last_result) > index:
    work_id = self.extract_work_id_from_url(session_data.last_result[index])
# Priority 3: Error - no fallback
else:
    raise Exception("No work ID available for download")
```

## Session Management and Connection Handling

### Connection Lifecycle
```mermaid
stateDiagram-v2
    [*] --> Disconnected
    Disconnected --> Connecting: connect()
    Connecting --> Connected: Success
    Connecting --> Disconnected: Failure
    Connected --> Generating: start_generation()
    Generating --> Connected: Success
    Generating --> Connected: Error
    Connected --> Disconnected: validate_connection() fails
```

### Multi-Session Connection Process
```python
# ConnectionWorker handles concurrent connections
with ThreadPoolExecutor(max_workers=5) as executor:
    futures = {executor.submit(connect_session, sid, data): sid 
              for sid, data in sessions.items()}
    
    for future in as_completed(futures):
        session_id, success, status, points = future.result()
        # Emit real-time updates to GUI
```

### Connection Validation Strategy
```python
# SessionData.validate_connection()
1. Check is_connected flag
2. Test API call: get_account_point()
3. Validate response is not None
4. Update connection status
5. Return boolean result
```

## Download System Architecture

### Two Download Paths

#### 1. Standard Download (With Watermark)
```python
# Direct URL download
DownloadWorkerThread:
    1. Get result URL from session_data.last_result[index]
    2. HTTP GET with streaming
    3. Write to file with progress tracking
    4. Retry on failure (up to 3 times)
```

#### 2. No-Watermark Download (API-based)
```python
# API-based download
NoWatermarkDownloadWorker:
    1. Get work_id (BUGGY - see above)
    2. Call batch_download_v2 API with work_id
    3. Extract cdnUrl from API response
    4. Download from cdnUrl with progress tracking
    5. Retry on failure (up to 3 times)
```

### Download Task State Management
```python
# DownloadTask states
"pending"     → Task created, not started
"downloading" → HTTP request in progress
"completed"   → File successfully downloaded
"failed"      → All retry attempts exhausted
```

### Auto-Download Logic
```python
# Triggered in on_result_ready()
if self.download_settings.auto_download_enabled:
    if self.download_settings.no_watermark_mode:
        # Use API-based download
        for i in range(len(results)):
            self.download_without_watermark(session_id, i)
    else:
        # Use standard download
        for i, url in enumerate(results):
            self.download_single_result(session_id, url, content_type, model, i)
```

## Sequential vs Parallel Execution

### Parallel Mode (Default)
```python
# All sessions start immediately
for session_id in selected_sessions:
    worker = UnifiedWorkerThread(session_data, ...)
    worker.start()  # Start immediately
```

### Sequential Mode
```python
# Queue-based execution
self.session_queue = list(selected_sessions)
self.current_session_index = 0
self.sequential_timer.start(self.sequential_delay)

def process_next_session():
    if self.current_session_index < len(self.session_queue):
        session = self.session_queue[self.current_session_index]
        # Start only current session
        # Timer continues to next session after delay
```

### Loop Functionality
```python
# Cycle management
self.total_cycles = user_input
self.current_cycle = 0
self.cycle_completion_tracker = {}

# Wait for all sessions to complete before next cycle
def check_cycle_completion():
    if all_sessions_completed():
        self.current_cycle += 1
        if self.current_cycle < self.total_cycles:
            time.sleep(custom_delay)
            start_next_cycle()
```

## Error Handling Patterns

### Layered Error Handling
```python
# Layer 1: Core API (kling.py)
try:
    response = self.session.post(url, json=payload)
    response.raise_for_status()
except requests.RequestException as e:
    raise Exception(f"API request failed: {e}")

# Layer 2: Safe Wrapper (kling_wrapper.py)
for attempt in range(max_retries):
    try:
        result = self.original.get_video(...)
        if result is None:
            raise Exception("API returned None")
        return result
    except Exception as e:
        if "NoneType" in str(e) and attempt < max_retries - 1:
            time.sleep(3)
            continue
        raise

# Layer 3: GUI (unified_multi_session.py)
try:
    worker.start()
except Exception as e:
    QMessageBox.critical(self, "Error", f"Generation failed: {e}")
    self.log_message(f"❌ Error: {e}")
```

### Connection Error Categories
```python
# Cookie-related errors
if "cookie" in error_msg.lower():
    self.status = "Invalid Cookie"

# Network errors
elif "connection" in error_msg.lower():
    self.status = "Network Error"

# API errors
elif "none" in error_msg.lower():
    self.status = "API Error"

# Generic errors
else:
    self.status = f"Error: {error_msg[:30]}..."
```

## Critical Code Paths

### 1. Generation Success Path
```
User Input → Validation → API Call → Work ID Storage → Result Display → Auto-Download
```

### 2. Generation Failure Path
```
User Input → Validation → API Call → Error → Retry Logic → Final Failure → User Notification
```

### 3. Download Success Path
```
Result Ready → Download Task → Worker Thread → Progress Updates → File Saved → UI Update
```

### 4. Download Failure Path
```
Result Ready → Download Task → Worker Thread → Error → Retry → Final Failure → User Notification
```
