# KlingCreator Architecture Overview

## System Design Philosophy

KlingCreator follows a **layered architecture** with clear separation of concerns:
- **GUI Layer**: PyQt6-based user interface (`unified_multi_session.py`)
- **Wrapper Layer**: Safe API abstraction (`kling_wrapper.py`) 
- **Core API Layer**: Direct Kling AI integration (`kling/kling.py`)

## High-Level Component Relationships

```mermaid
graph TB
    GUI[UnifiedMultiSessionGUI<br/>unified_multi_session.py]
    WRAPPER[Safe Wrappers<br/>kling_wrapper.py]
    CORE[Core API<br/>kling/kling.py]
    
    GUI --> WRAPPER
    WRAPPER --> CORE
    
    subgraph "GUI Components"
        SESSION[SessionData]
        DOWNLOAD[DownloadSettings]
        WORKERS[Worker Threads]
    end
    
    subgraph "Wrapper Components"
        SAFEVIDEO[SafeVideoGen]
        SAFEIMAGE[SafeImageGen]
    end
    
    subgraph "Core Components"
        VIDEOGEN[VideoGen]
        IMAGEGEN[ImageGen]
        BASEGEN[BaseGen]
    end
    
    GUI --> SESSION
    GUI --> DOWNLOAD
    GUI --> WORKERS
    
    WRAPPER --> SAFEVIDEO
    WRAPPER --> SAFEIMAGE
    
    CORE --> VIDEOGEN
    CORE --> IMAGEGEN
    CORE --> BASEGEN
```

## Data Flow Architecture

### 1. Generation Workflow
```
User Input → GUI → SessionData → UnifiedWorkerThread → SafeWrapper → Core API → Kling AI
                                                                                    ↓
Results ← GUI ← SessionData ← UnifiedWorkerThread ← SafeWrapper ← Core API ← Kling AI
```

### 2. Download Workflow
```
Results → DownloadTask → DownloadWorkerThread → File System
       → NoWatermarkDownloadWorker → batch_download_v2 API → File System
```

### 3. Session Management
```
Cookie Input → SessionData.connect() → SafeWrapper → Core API → Connection Status
```

## Threading Model

### Thread Types and Responsibilities

| Thread Type | Purpose | Lifecycle | Communication |
|-------------|---------|-----------|---------------|
| **ConnectionWorker** | Multi-session connection | Short-lived | Signals for status updates |
| **UnifiedWorkerThread** | Content generation | Medium-lived | Progress/result signals |
| **DownloadWorkerThread** | Standard downloads | Short-lived | Progress/completion signals |
| **NoWatermarkDownloadWorker** | API-based downloads | Short-lived | Progress/completion signals |

### Threading Architecture

```mermaid
graph LR
    MAIN[Main GUI Thread]
    
    subgraph "Worker Threads"
        CONN[ConnectionWorker]
        GEN[UnifiedWorkerThread]
        DL[DownloadWorkerThread]
        NWDL[NoWatermarkDownloadWorker]
    end
    
    MAIN -->|spawn| CONN
    MAIN -->|spawn| GEN
    MAIN -->|spawn| DL
    MAIN -->|spawn| NWDL
    
    CONN -->|signals| MAIN
    GEN -->|signals| MAIN
    DL -->|signals| MAIN
    NWDL -->|signals| MAIN
```

### Thread Communication Patterns

**Signal-Slot Pattern (PyQt6)**:
- All worker threads communicate with GUI via Qt signals
- No direct thread-to-thread communication
- Thread-safe data sharing through Qt's signal system

**Key Signals**:
- `progress_update(session_id, message, progress)`
- `result_ready(session_id, results, content_type)`
- `download_completed(task_id, file_path)`
- `session_connected(session_id, success, status, points)`

## Module Interaction Patterns

### 1. unified_multi_session.py ↔ kling_wrapper.py

**Purpose**: Safe API abstraction with error handling

```python
# GUI creates safe wrappers
session_data.image_gen = ImageGen(cookie)  # Actually SafeImageGen
session_data.video_gen = VideoGen(cookie)  # Actually SafeVideoGen

# Wrapper provides retry logic and validation
results = session_data.video_gen.get_video(prompt, **kwargs)
```

**Key Benefits**:
- NoneType error protection
- Automatic retry logic
- Input validation
- Connection testing

### 2. kling_wrapper.py ↔ kling/kling.py

**Purpose**: Direct API access with safety layer

```python
# Wrapper delegates to original with error handling
class SafeVideoGen:
    def __init__(self, cookie):
        self.original = OriginalVideoGen(cookie)
    
    def get_video(self, prompt, **kwargs):
        # Validation + retry logic
        return self.original.get_video(prompt, **kwargs)
```

**Key Benefits**:
- Preserves original API unchanged
- Adds reliability layer
- Maintains work ID access via properties

## State Management

### Session State Flow
```
Disconnected → Connecting → Connected → Generating → Completed
     ↑                                      ↓
     └─────────── Error Handling ←─────────┘
```

### Download State Flow
```
Pending → Downloading → Completed
   ↓           ↓
Failed ←── Retrying
```

## Error Handling Strategy

### Layered Error Handling
1. **GUI Layer**: User-friendly error messages, UI state management
2. **Wrapper Layer**: Retry logic, connection validation, NoneType protection
3. **Core Layer**: HTTP errors, API response validation

### Error Propagation
```
Core API Error → Wrapper (retry/handle) → GUI (user notification)
```

## Performance Considerations

### Concurrent Operations
- **Multi-session connections**: Up to 5 concurrent connections
- **Parallel generation**: Multiple sessions can generate simultaneously
- **Background downloads**: Non-blocking download operations

### Resource Management
- **Thread cleanup**: Automatic cleanup of finished threads
- **Memory management**: Proper disposal of large download data
- **Connection pooling**: Reuse of HTTP sessions

## Scalability Design

### Horizontal Scaling
- **Session-based**: Each cookie represents an independent session
- **Thread pooling**: Configurable worker thread limits
- **Queue-based**: Sequential mode uses session queues

### Vertical Scaling
- **Async operations**: Non-blocking UI during long operations
- **Progress tracking**: Real-time feedback for all operations
- **Resource limits**: Configurable retry counts and timeouts

## Security Considerations

### Cookie Management
- **Secure storage**: Cookies stored in memory only during session
- **Validation**: Cookie format validation before use
- **Isolation**: Each session maintains independent cookie state

### API Security
- **Rate limiting**: Built-in delays between requests
- **Error masking**: Sensitive information not exposed in logs
- **Connection validation**: Regular connection health checks
