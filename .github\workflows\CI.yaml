name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

concurrency:
  group: ${{ github.event.number || github.run_id }}
  cancel-in-progress: true

jobs:
  testing:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: install python 3.9
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"
          cache: "pip" # caching pip dependencies
      - name: Check formatting (black) and tests
        run: |
          pip install -r requirements.txt
          pip install black pytest
          black . --check
          pytest
