# KlingCreator API Reference

## Class Hierarchy Overview

```
BaseGen (kling/kling.py)
├── VideoGen (kling/kling.py)
└── ImageGen (kling/kling.py)

SafeVideoGen (kling_wrapper.py) → wraps VideoGen
SafeImageGen (kling_wrapper.py) → wraps ImageGen

UnifiedMultiSessionGUI (unified_multi_session.py)
├── SessionData
├── DownloadSettings
├── DownloadTask
├── ConnectionWorker (QThread)
├── UnifiedWorkerThread (QThread)
├── DownloadWorkerThread (QThread)
└── NoWatermarkDownloadWorker (QThread)
```

## Core Classes

### SessionData
**Purpose**: Manages individual cookie session state and API connections

#### Key Properties
```python
session_id: str              # Unique session identifier
cookie: str                  # Authentication cookie
image_gen: SafeImageGen      # Image generation wrapper
video_gen: SafeVideoGen      # Video generation wrapper
points: float                # Account points/credits
is_connected: bool           # Connection status
status: str                  # Human-readable status
current_task: str            # Current operation description
progress: int                # Task progress (0-100)
last_result: List[str]       # Generated content URLs
last_content_type: str       # "image" or "video"
last_model: str              # Model version used
last_work_ids: List[str]     # Work IDs for no-watermark downloads
```

#### Key Methods
```python
connect() -> bool
    """Establish connection and validate cookie"""
    
validate_connection() -> bool
    """Check if existing connection is still valid"""
```

### DownloadSettings
**Purpose**: Manages download configuration and persistence

#### Key Properties
```python
download_path: str           # Download directory
auto_download_enabled: bool  # Auto-download after generation
no_watermark_mode: bool      # Default download mode
max_retries: int             # Download retry attempts
retry_delay: float           # Delay between retries
```

#### Key Methods
```python
save_settings()
    """Persist settings to QSettings"""
    
ensure_download_directory() -> bool
    """Create download directory if needed"""
```

### DownloadTask
**Purpose**: Represents a single download operation

#### Key Properties
```python
session_id: str              # Source session
url: str                     # Download URL
content_type: str            # "image" or "video"
model: str                   # Model version
index: int                   # Result index
no_watermark: bool           # Download mode
status: str                  # "pending", "downloading", "completed", "failed"
progress: int                # Download progress (0-100)
file_path: str               # Destination file path
error_message: str           # Error details if failed
retry_count: int             # Current retry attempt
```

#### Key Methods
```python
generate_filename(download_path: str) -> str
    """Create unique filename with timestamp and metadata"""
```

## Worker Thread Classes

### ConnectionWorker (QThread)
**Purpose**: Multi-threaded session connection with concurrency control

#### Signals
```python
session_connected(str, bool, str, float)  # session_id, success, status, points
all_connections_complete(int, int)        # connected_count, total_count
```

#### Key Methods
```python
__init__(sessions_dict, max_workers=5)
    """Initialize with session dictionary and worker limit"""
    
connect_single_session(session_id, session_data) -> tuple
    """Connect individual session (runs in thread pool)"""
```

### UnifiedWorkerThread (QThread)
**Purpose**: Content generation with progress tracking

#### Signals
```python
progress_update(str, str, int)    # session_id, message, progress
result_ready(str, list, str)      # session_id, results, content_type
error_occurred(str, str)          # session_id, error_message
```

#### Key Methods
```python
__init__(session_data, task_type, prompt, image_path=None, **kwargs)
    """Initialize generation task"""
    
run()
    """Execute generation with validation and error handling"""
```

### DownloadWorkerThread (QThread)
**Purpose**: Standard file download with retry logic

#### Signals
```python
download_progress(str, int)       # task_id, progress
download_completed(str, str)      # task_id, file_path
download_failed(str, str)         # task_id, error_message
```

### NoWatermarkDownloadWorker (QThread)
**Purpose**: API-based no-watermark downloads

#### Key Methods
```python
__init__(cookie, work_id, download_task, download_path, max_retries=3)
    """Initialize with API credentials and task details"""
    
get_download_url(with_watermark=False) -> str
    """Call batch_download_v2 API to get download URL"""
```

## Safe Wrapper Classes

### SafeVideoGen
**Purpose**: Error-safe wrapper around VideoGen

#### Key Methods
```python
get_account_point() -> Optional[float]
    """Safe account points retrieval with None protection"""
    
get_video(prompt, image_path=None, image_url=None, 
          is_high_quality=False, auto_extend=False, 
          model_name="1.0") -> List[str]
    """Safe video generation with retry logic and validation"""
    
image_uploader(image_path: str) -> str
    """Safe image upload with file validation"""
    
@property
last_work_ids -> List[str]
    """Access work IDs from original generator"""
```

### SafeImageGen
**Purpose**: Error-safe wrapper around ImageGen

#### Key Methods
```python
get_account_point() -> Optional[float]
    """Safe account points retrieval"""
    
get_images(prompt, image_path=None, image_url=None) -> List[str]
    """Safe image generation with retry logic"""
    
image_uploader(image_path: str) -> str
    """Safe image upload"""
    
@property
last_work_ids -> List[str]
    """Access work IDs from original generator"""
```

## Core API Classes (kling/kling.py)

### BaseGen
**Purpose**: Base class with common functionality

#### Key Methods
```python
__init__(cookie: str)
    """Initialize session with cookie parsing and daily check"""
    
parse_cookie_string(cookie_string) -> tuple
    """Parse cookie string and determine region (CN/non-CN)"""
    
get_account_point() -> float
    """Retrieve account points/credits"""
    
image_uploader(image_path: str) -> str
    """Upload image and return URL"""
    
fetch_metadata(task_id: str) -> tuple[dict, TaskStatus]
    """Get task status and metadata"""
```

### VideoGen (extends BaseGen)
**Purpose**: Video generation implementation

#### Key Methods
```python
get_video(prompt, image_path=None, image_url=None,
          is_high_quality=False, auto_extend=False,
          model_name="1.0") -> list
    """Generate video with specified parameters"""
    
save_video(prompt, output_dir, **kwargs)
    """Generate and save video to directory"""
    
extend_video(video_id: int, prompt="") -> str
    """Extend existing video"""
    
_get_video_with_payload(payload: dict) -> list
    """Internal method for video generation with payload"""
```

### ImageGen (extends BaseGen)
**Purpose**: Image generation implementation

#### Key Methods
```python
get_images(prompt, image_path=None, image_url=None) -> list
    """Generate images with specified parameters"""
    
save_images(prompt, output_dir, **kwargs)
    """Generate and save images to directory"""
```

## Utility Classes

### CookieManager
**Purpose**: Cookie file operations

#### Static Methods
```python
load_cookies_from_file(file_path: str) -> List[str]
    """Load cookies from text file"""
    
validate_cookie(cookie: str) -> bool
    """Validate cookie format"""
    
save_cookies_to_file(cookies: List[str], file_path: str)
    """Save cookies to text file"""
```

### PromptManager
**Purpose**: Prompt file operations

#### Static Methods
```python
load_prompts_from_file(file_path: str) -> List[str]
    """Load prompts from text file"""
    
get_random_prompt(prompts: List[str]) -> str
    """Select random prompt from list"""
```

### ImageManager
**Purpose**: Image file operations

#### Static Methods
```python
get_supported_formats() -> List[str]
    """Return supported image formats"""
    
is_valid_image(file_path: str) -> bool
    """Validate image file"""
    
get_images_from_folder(folder_path: str) -> List[str]
    """Get all valid images from folder"""
    
get_random_image(folder_path: str) -> Optional[str]
    """Select random image from folder"""
```

## Global Variables

### kling/kling.py
```python
LAST_WORK_IDS: List[str]     # Global work ID storage
browser_version: str         # Browser version for user agent
base_url: str                # API base URL (CN)
base_url_not_cn: str         # API base URL (non-CN)
```

## Enums

### TaskStatus (kling/kling.py)
```python
PENDING = 0      # Task in progress
COMPLETED = 1    # Task finished successfully
FAILED = 3       # Task failed
```

## Key Constants

### Model Versions
- Video: "1.0", "1.5", "1.6", "2.1"
- Image: N/A (single model)

### Content Types
- "image": Image generation
- "video": Video generation

### Download Modes
- Watermarked: Standard download from result URL
- No-watermark: API-based download via batch_download_v2
