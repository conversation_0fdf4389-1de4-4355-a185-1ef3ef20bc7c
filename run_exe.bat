@echo off
echo 🚀 Starting KlingCreator.exe...
echo.
cd /d "%~dp0"
if exist "dist\KlingCreator.exe" (
    echo ✅ Found KlingCreator.exe
    echo 📂 Location: %cd%\dist\KlingCreator.exe
    echo 📊 Starting application...
    echo.
    start "" "dist\KlingCreator.exe"
    echo ✨ KlingCreator launched successfully!
) else (
    echo ❌ KlingCreator.exe not found in dist folder!
    echo 💡 Please run build_exe.py first to create the .exe file
)
echo.
pause
