#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Kling Wrapper - Safe wrapper around original kling module
Handles NoneType errors without modifying original code
"""

import sys
import os
import time
from typing import List, Optional

# Import original kling module
from kling import VideoGen as OriginalVideoGen, ImageGen as OriginalImageGen


class SafeVideoGen:
    """Safe wrapper around VideoGen to handle NoneType errors"""
    
    def __init__(self, cookie: str):
        self.original = OriginalVideoGen(cookie)
        self.cookie = cookie
        
    def get_account_point(self) -> Optional[float]:
        """Safe wrapper for get_account_point"""
        try:
            result = self.original.get_account_point()
            if result is None:
                raise Exception("API returned None for account points")
            return result
        except Exception as e:
            print(f"SafeVideoGen.get_account_point error: {e}")
            return None
    
    def get_video(
        self,
        prompt: str,
        image_path: Optional[str] = None,
        image_url: Optional[str] = None,
        is_high_quality: bool = False,
        auto_extend: bool = False,
        model_name: str = "1.0",
        image_count: int = 1,
    ) -> List[str]:
        """Safe wrapper for get_video"""
        try:
            # Validate inputs
            if not prompt or not prompt.strip():
                raise Exception("Prompt cannot be empty")
            
            # Pre-check connection
            points = self.get_account_point()
            if points is None:
                raise Exception("Cannot verify account - connection may be lost")
            
            if points <= 0:
                raise Exception(f"Insufficient points: {points}")
            
            print(f"SafeVideoGen: Starting generation with {points} points")
            
            # Call original method with retry logic
            max_retries = 2
            for attempt in range(max_retries):
                try:
                    result = self.original.get_video(
                        prompt=prompt,
                        image_path=image_path,
                        image_url=image_url,
                        is_high_quality=is_high_quality,
                        auto_extend=auto_extend,
                        model_name=model_name,
                        image_count=image_count
                    )
                    
                    # Validate result
                    if result is None:
                        raise Exception("API returned None result")
                    
                    if not isinstance(result, list):
                        raise Exception(f"Expected list, got {type(result)}")
                    
                    if len(result) == 0:
                        raise Exception("No videos generated")
                    
                    print(f"SafeVideoGen: Successfully generated {len(result)} videos")
                    return result
                    
                except Exception as e:
                    error_msg = str(e)
                    print(f"SafeVideoGen attempt {attempt + 1} failed: {error_msg}")
                    
                    # Check if it's a retryable error
                    if "NoneType" in error_msg or "None" in error_msg:
                        if attempt < max_retries - 1:
                            print(f"SafeVideoGen: Retrying in 3 seconds...")
                            time.sleep(3)
                            continue
                    
                    # Re-raise if not retryable or max retries reached
                    raise Exception(f"Video generation failed: {error_msg}")
            
            raise Exception("Max retries exceeded")
            
        except Exception as e:
            print(f"SafeVideoGen.get_video error: {e}")
            raise
    
    def image_uploader(self, image_path: str) -> str:
        """Safe wrapper for image_uploader"""
        try:
            if not os.path.exists(image_path):
                raise Exception(f"Image file not found: {image_path}")
            
            result = self.original.image_uploader(image_path)
            
            if result is None:
                raise Exception("Image upload returned None")
            
            if not isinstance(result, str) or not result.strip():
                raise Exception(f"Invalid upload result: {result}")
            
            print(f"SafeVideoGen: Image uploaded successfully")
            return result
            
        except Exception as e:
            print(f"SafeVideoGen.image_uploader error: {e}")
            raise
    
    @property
    def last_work_ids(self):
        """Get last work IDs from original generator"""
        return getattr(self.original, 'last_work_ids', [])


class SafeImageGen:
    """Safe wrapper around ImageGen to handle NoneType errors"""
    
    def __init__(self, cookie: str):
        self.original = OriginalImageGen(cookie)
        self.cookie = cookie
        
    def get_account_point(self) -> Optional[float]:
        """Safe wrapper for get_account_point"""
        try:
            result = self.original.get_account_point()
            if result is None:
                raise Exception("API returned None for account points")
            return result
        except Exception as e:
            print(f"SafeImageGen.get_account_point error: {e}")
            return None
    
    def get_images(
        self,
        prompt: str,
        image_path: Optional[str] = None,
        image_url: Optional[str] = None,
    ) -> List[str]:
        """Safe wrapper for get_images"""
        try:
            # Validate inputs
            if not prompt or not prompt.strip():
                raise Exception("Prompt cannot be empty")
            
            # Pre-check connection
            points = self.get_account_point()
            if points is None:
                raise Exception("Cannot verify account - connection may be lost")
            
            if points <= 0:
                raise Exception(f"Insufficient points: {points}")
            
            print(f"SafeImageGen: Starting generation with {points} points")
            
            # Call original method with retry logic
            max_retries = 2
            for attempt in range(max_retries):
                try:
                    result = self.original.get_images(
                        prompt=prompt,
                        image_path=image_path,
                        image_url=image_url
                    )
                    
                    # Validate result
                    if result is None:
                        raise Exception("API returned None result")
                    
                    if not isinstance(result, list):
                        raise Exception(f"Expected list, got {type(result)}")
                    
                    if len(result) == 0:
                        raise Exception("No images generated")
                    
                    print(f"SafeImageGen: Successfully generated {len(result)} images")
                    return result
                    
                except Exception as e:
                    error_msg = str(e)
                    print(f"SafeImageGen attempt {attempt + 1} failed: {error_msg}")
                    
                    # Check if it's a retryable error
                    if "NoneType" in error_msg or "None" in error_msg:
                        if attempt < max_retries - 1:
                            print(f"SafeImageGen: Retrying in 3 seconds...")
                            time.sleep(3)
                            continue
                    
                    # Re-raise if not retryable or max retries reached
                    raise Exception(f"Image generation failed: {error_msg}")
            
            raise Exception("Max retries exceeded")
            
        except Exception as e:
            print(f"SafeImageGen.get_images error: {e}")
            raise
    
    def image_uploader(self, image_path: str) -> str:
        """Safe wrapper for image_uploader"""
        try:
            if not os.path.exists(image_path):
                raise Exception(f"Image file not found: {image_path}")
            
            result = self.original.image_uploader(image_path)
            
            if result is None:
                raise Exception("Image upload returned None")
            
            if not isinstance(result, str) or not result.strip():
                raise Exception(f"Invalid upload result: {result}")
            
            print(f"SafeImageGen: Image uploaded successfully")
            return result
            
        except Exception as e:
            print(f"SafeImageGen.image_uploader error: {e}")
            raise
    
    @property
    def last_work_ids(self):
        """Get last work IDs from original generator"""
        return getattr(self.original, 'last_work_ids', [])


# Convenience aliases to replace original imports
VideoGen = SafeVideoGen
ImageGen = SafeImageGen
