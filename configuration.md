# KlingCreator Configuration & Settings

## QSettings Persistence Mechanism

### Settings Storage Location
```python
# QSettings initialization
self.settings = QSettings("KlingCreator", "UnifiedMultiSession")

# Windows: HKEY_CURRENT_USER\Software\KlingCreator\UnifiedMultiSession
# Linux: ~/.config/KlingCreator/UnifiedMultiSession.conf
# macOS: ~/Library/Preferences/com.KlingCreator.UnifiedMultiSession.plist
```

### Settings Architecture
```python
class DownloadSettings:
    def __init__(self):
        self.settings = QSettings("KlingCreator", "UnifiedMultiSession")
        # Load with defaults
        self.download_path = self.settings.value("download_path", "./downloads")
        self.auto_download_enabled = self.settings.value("auto_download_enabled", True, type=bool)
        self.no_watermark_mode = self.settings.value("no_watermark_mode", False, type=bool)
    
    def save_settings(self):
        # Persist to registry/config file
        self.settings.setValue("download_path", self.download_path)
        self.settings.setValue("auto_download_enabled", self.auto_download_enabled)
        self.settings.setValue("no_watermark_mode", self.no_watermark_mode)
```

## Download Settings Configuration

### Core Download Settings
| Setting | Type | Default | Purpose |
|---------|------|---------|---------|
| `download_path` | str | "./downloads" | Target directory for downloads |
| `auto_download_enabled` | bool | True | Auto-download after generation |
| `no_watermark_mode` | bool | False | Default download mode |
| `max_retries` | int | 3 | Download retry attempts |
| `retry_delay` | float | 1.0 | Delay between retries (seconds) |

### Settings Persistence Flow
```mermaid
sequenceDiagram
    participant User
    participant GUI
    participant DownloadSettings
    participant QSettings
    participant Storage

    User->>GUI: Change Setting
    GUI->>DownloadSettings: Update Property
    DownloadSettings->>QSettings: setValue()
    QSettings->>Storage: Write to Registry/File
    
    Note over Storage: Settings Persisted
    
    User->>GUI: Restart Application
    GUI->>DownloadSettings: __init__()
    DownloadSettings->>QSettings: value() with default
    QSettings->>Storage: Read from Registry/File
    Storage-->>DownloadSettings: Restored Value
```

### Download Path Management
```python
def ensure_download_directory(self) -> bool:
    """Create and validate download directory"""
    if not os.path.exists(self.download_path):
        os.makedirs(self.download_path, exist_ok=True)
    
    # Validate write permissions
    return os.path.exists(self.download_path) and os.access(self.download_path, os.W_OK)

def validate_download_path(self) -> bool:
    """Check if download path is valid and writable"""
    return self.download_settings.ensure_download_directory()
```

## User Preferences

### GUI State Persistence
```python
# Window geometry and splitter positions
self.settings.setValue("window_geometry", self.saveGeometry())
self.settings.setValue("splitter_state", main_splitter.saveState())

# Restore on startup
geometry = self.settings.value("window_geometry")
if geometry:
    self.restoreGeometry(geometry)
```

### Input Mode Preferences
```python
# Image selection mode
self.image_mode = self.settings.value("image_mode", "single_file")  # "single_file" or "folder"

# Prompt input mode  
self.prompt_mode = self.settings.value("prompt_mode", "manual")     # "manual" or "file"

# Generation mode
self.generation_mode = self.settings.value("generation_mode", "parallel")  # "parallel" or "sequential"
```

### Model and Quality Preferences
```python
# Last used model
self.last_model = self.settings.value("last_model", "1.0")

# Quality settings
self.high_quality_default = self.settings.value("high_quality_default", False, type=bool)
self.auto_extend_default = self.settings.value("auto_extend_default", False, type=bool)
```

## Cookie Management and Validation

### Cookie Storage Strategy
```python
# Cookies are NOT persisted for security reasons
# Only stored in memory during session
class SessionData:
    def __init__(self, session_id: str, cookie: str):
        self.cookie = cookie  # Memory only, not saved to QSettings
```

### Cookie Validation Logic
```python
@staticmethod
def validate_cookie(cookie: str) -> bool:
    """Validate cookie format and content"""
    # Basic format validation
    if not cookie or len(cookie) < 50:
        return False
    
    # Must contain key=value pairs
    if '=' not in cookie:
        return False
    
    # Additional validation in SessionData.connect()
    return True

def connect(self) -> bool:
    """Enhanced cookie validation during connection"""
    try:
        # Validate format first
        if not CookieManager.validate_cookie(self.cookie):
            raise Exception("Invalid cookie format")
        
        # Test actual connection
        self.image_gen = ImageGen(self.cookie)
        points = self.image_gen.get_account_point()
        
        if points is None:
            raise Exception("Cookie authentication failed")
        
        self.points = float(points)
        self.is_connected = True
        return True
        
    except Exception as e:
        self.is_connected = False
        self.status = self.categorize_error(str(e))
        return False
```

### Cookie File Operations
```python
class CookieManager:
    @staticmethod
    def load_cookies_from_file(file_path: str) -> List[str]:
        """Load cookies from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                cookies = [line.strip() for line in f.readlines() if line.strip()]
            return cookies
        except Exception as e:
            raise Exception(f"Error loading cookies: {e}")
    
    @staticmethod
    def save_cookies_to_file(cookies: List[str], file_path: str):
        """Export cookies to text file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for cookie in cookies:
                    f.write(cookie + '\n')
        except Exception as e:
            raise Exception(f"Error saving cookies: {e}")
```

## Application Configuration

### Threading Configuration
```python
# Connection worker limits
MAX_CONCURRENT_CONNECTIONS = 5

# Download retry configuration
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 1.0  # seconds
EXPONENTIAL_BACKOFF = True  # (2 ** attempt) * base_delay

# Sequential mode timing
DEFAULT_SEQUENTIAL_DELAY = 2000  # milliseconds between sessions
```

### API Configuration
```python
# Timeout settings
CONNECTION_TIMEOUT = 30      # seconds for API calls
DOWNLOAD_TIMEOUT = 60        # seconds for file downloads
GENERATION_TIMEOUT = 1200    # seconds for generation (20 minutes)

# Polling intervals
STATUS_POLL_INTERVAL = 5     # seconds between status checks
PROGRESS_UPDATE_INTERVAL = 100  # milliseconds for UI updates
```

### File Management Configuration
```python
# Supported file formats
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']

# Filename pattern
FILENAME_PATTERN = "{session_id}_{timestamp}_{content_type}_{model}_{index:03d}{watermark_suffix}{extension}"

# Directory structure
DEFAULT_DOWNLOAD_DIR = "./downloads"
TEMP_DIR = "./temp"  # For intermediate files
```

## Loop and Cycle Configuration

### Cycle Management Settings
```python
# Loop functionality
self.total_cycles = 1                    # Number of cycles to run
self.current_cycle = 0                   # Current cycle index
self.cycle_delay = 0                     # Custom delay between cycles (seconds)
self.sequential_delay = 2000             # Delay between sessions (milliseconds)

# Cycle completion tracking
self.cycle_completion_tracker = {}       # Track session completion per cycle
self.waiting_for_cycle_completion = False
```

### Sequential Mode Configuration
```python
# Sequential execution settings
self.sequential_mode = True              # Enable sequential processing
self.session_queue = []                  # Queue of sessions to process
self.current_session_index = 0           # Current position in queue

# Timer configuration
self.sequential_timer = QTimer()
self.sequential_timer.timeout.connect(self.process_next_session)
```

## Error Handling Configuration

### Error Categorization
```python
def categorize_error(self, error_msg: str) -> str:
    """Categorize errors for user-friendly display"""
    error_lower = error_msg.lower()
    
    if "cookie" in error_lower:
        return "Invalid Cookie"
    elif "connection" in error_lower or "network" in error_lower:
        return "Network Error"
    elif "none" in error_lower or "api" in error_lower:
        return "API Error"
    elif "timeout" in error_lower:
        return "Timeout Error"
    elif "points" in error_lower or "credits" in error_lower:
        return "Insufficient Credits"
    else:
        return f"Error: {error_msg[:30]}..."
```

### Logging Configuration
```python
# Log message formatting
LOG_TIMESTAMP_FORMAT = "%H:%M:%S"
LOG_MESSAGE_FORMAT = "[{timestamp}] {level} {message}"

# Log levels (visual indicators)
LOG_LEVELS = {
    "info": "ℹ️",
    "success": "✅", 
    "warning": "⚠️",
    "error": "❌",
    "debug": "🔍"
}
```

## Security Configuration

### Cookie Security
```python
# Cookie display in UI
COOKIE_DISPLAY_MODE = QLineEdit.EchoMode.Password  # Hide cookie content

# Cookie validation
MIN_COOKIE_LENGTH = 50
REQUIRED_COOKIE_PATTERNS = ['=']  # Must contain key=value pairs

# Session isolation
COOKIE_MEMORY_ONLY = True  # Never persist cookies to disk
```

### API Security
```python
# Rate limiting
MIN_REQUEST_INTERVAL = 0.1   # Minimum seconds between API calls
MAX_CONCURRENT_REQUESTS = 5  # Maximum simultaneous API requests

# Error information filtering
HIDE_SENSITIVE_ERRORS = True  # Don't expose internal API details
MASK_COOKIE_IN_LOGS = True   # Replace cookie with "***" in logs
```

## Build Configuration

### PyInstaller Settings
```python
# Build configuration in build_exe.py
BUILD_SETTINGS = {
    "onefile": True,           # Single executable
    "windowed": True,          # No console window
    "name": "KlingCreator_MultiThread_v2",
    "exclude_modules": [       # Reduce file size
        "matplotlib", "numpy", "pandas", 
        "scipy", "tensorflow", "torch"
    ]
}
```

### Dependencies
```python
# requirements.txt
REQUIRED_PACKAGES = [
    "requests>=2.28.0",
    "rich>=12.0.0", 
    "fake-useragent>=1.1.0",
    "PyQt6>=6.4.0",
    "Pillow>=9.0.0",
    "pyinstaller>=5.0.0"
]
```
